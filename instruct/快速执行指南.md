# 纯问题集问答重建 - 快速执行指南

## 🎯 任务目标
基于 `工作目录/纯问题集重建/` 中的30个问题文件，生成对应的高质量问答对话。

## 📁 文件结构理解

### 当前结构
```
工作目录/纯问题集重建/
├── 11维度.md (包含Q1-Q6)
├── Apple外区id指南.md (包含Q1)
├── telegram创始人发言分析.md (包含Q1-Q6)
├── ... (其他27个问题文件)
└── README.md
```

### 目标结构
```
工作目录/纯问题集重建/
├── [原有问题文件...]
└── 重建回答/ (新建)
    ├── README.md
    ├── 进度报告.md
    ├── 11维度_回答.md
    ├── Apple外区id指南_回答.md
    └── ... (30个回答文件)
```

## 🔄 执行步骤

### Step 1: 环境准备
```bash
1. 创建文件夹: 工作目录/纯问题集重建/重建回答/
2. 创建进度跟踪文件
3. 初始化任务管理系统 (使用add_tasks工具)
```

### Step 2: 批量处理
对每个问题文件执行：
1. **读取问题文件** - 解析Q1, Q2, Q3...
2. **生成对应回答** - 创建A1, A2, A3...
3. **保持上下文连贯** - A2基于Q1-A1, A3基于Q1-A1,Q2-A2...
4. **更新任务状态** - 标记为COMPLETE

### Step 3: 格式规范

#### 回答文件格式
```markdown
# [主题名] - 重建回答

## 问答对话重建

### Q1
[原始问题内容]

### A1
[高质量回答内容]

### Q2
[原始问题内容]

### A2
[基于上下文的回答内容]

...

## 文件处理信息
- **原始问题数**: X个
- **生成回答数**: X个
- **主要问题类型**: [类型]
- **处理时间**: [时间戳]
```

## 📋 问题类型处理策略

### YouTube视频转写类 (60%)
**回答要点**:
- 技术实现方案 (Whisper, API等)
- 工具推荐和使用方法
- 质量控制步骤
- 格式化技巧

### 深度分析类 (25%)
**回答要点**:
- 多维度分析框架
- 理论基础和实例
- 数据事实支撑
- 平衡观点呈现

### 对话整理类 (10%)
**回答要点**:
- Markdown格式规范
- 自动化工具推荐
- 质量检查方法

### 专业咨询类 (5%)
**回答要点**:
- 具体操作步骤
- 工具资源推荐
- 注意事项提示

## ⚡ 快速执行命令

```
# 立即开始执行
1. save-file: 创建 重建回答/README.md
2. save-file: 创建 重建回答/进度报告.md
3. add_tasks: 为30个文件创建处理任务
4. 循环处理每个问题文件:
   - view: 读取问题文件
   - 分析Q1-Qn
   - 生成A1-An (保持上下文)
   - save-file: 创建对应回答文件
   - update_tasks: 标记任务完成
5. 更新最终进度报告
```

## 🎯 质量标准

### 最低要求
- ✅ 每个Q都有对应的A
- ✅ 每个回答至少200字
- ✅ 保持上下文连贯性
- ✅ 提供实用价值

### 优秀标准
- ⭐ 回答准确专业
- ⭐ 内容完整全面
- ⭐ 格式规范统一
- ⭐ 上下文自然流畅

## 🚀 开始执行

**准备好了吗？** 请按照以上步骤开始执行纯问题集的问答重建任务！

**预期成果**: 30个高质量问答文件，220+个专业回答，完整的问答数据集。

---
**执行提示**: 建议分批处理，每5个文件进行一次进度检查。遇到问题及时记录在进度报告中。
