# 平台发布顺序演变 - 纯问题集（重建版）

## Q1
感觉现在时代变了，很久以前，早在移动互联网初期，绝大多数互联网企业toc的产品，包含ui交互的系统，其产品兼容支持的平台一般都是按照web -linux -win -andriod -macos -ios这样的顺序，可能也反映了开发者手中最常用的平台。

但是近些年来，自从m系列芯片算力突出，ios用户群体巩固，macos允许三方下载app脱离app store的复杂审核之后，尤其是以llm为代表的一系列衍生产品（agent,浏览器），似乎开始沿着 web - macos/linux -ios - andriod -win 的顺序来发布。当然具体的顺序我可能观察不清。

请你就上述现象进行复杂的调研和分析，结合数据、事实，开发者反馈，最终解释这一现象变化的逻辑成因

## Q2
这个顺序是否有异议？你能修正微调更正确的变化前后的顺序吗？
